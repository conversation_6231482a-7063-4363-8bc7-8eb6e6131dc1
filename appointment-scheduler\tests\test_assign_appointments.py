"""
Tests for the AssignAppointment job.
"""

import pytest
from datetime import date, timedelta
from appointment_scheduler.jobs.assign_appointments import AssignAppointmentJob
from appointment_scheduler.config_manager import ConfigManager


class TestAssignAppointmentJob:
    """Test cases for AssignAppointment job."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config_manager = ConfigManager()
        self.job = AssignAppointmentJob(self.config_manager)
    
    def test_job_initialization(self):
        """Test that the job initializes correctly."""
        assert self.job is not None
        assert self.job.config_manager is not None
        assert self.job.scheduler_config is not None
        assert self.job.service_configs is not None
        assert self.job.solver_factory is not None
        assert self.job.solution_manager is not None
    
    def test_run_job_with_default_parameters(self):
        """Test running the job with default parameters."""
        result = self.job.run()
        
        # Verify result structure
        assert isinstance(result, dict)
        assert "success" in result
        assert "processing_time" in result
        
        if result["success"]:
            assert "summary" in result
            assert "assignments" in result
            assert "service_type_statistics" in result
            assert "constraint_summary" in result
            
            # Verify summary structure
            summary = result["summary"]
            assert "total_appointments" in summary
            assert "assigned_appointments" in summary
            assert "unassigned_appointments" in summary
            assert "assignment_rate_percent" in summary
            
            # Verify assignment rate is reasonable
            if summary["total_appointments"] > 0:
                assignment_rate = summary["assignment_rate_percent"]
                assert 0 <= assignment_rate <= 100
    
    def test_run_job_with_target_date(self):
        """Test running the job with a specific target date."""
        target_date = date.today() + timedelta(days=1)
        result = self.job.run(target_date=target_date)
        
        assert isinstance(result, dict)
        assert "success" in result
        assert "processing_time" in result
        
        # Processing time should be reasonable (less than 2 minutes)
        assert result["processing_time"] < 120
    
    def test_run_job_with_service_type_filter(self):
        """Test running the job with service type filtering."""
        result = self.job.run(service_type="skilled_nursing")
        
        assert isinstance(result, dict)
        assert "success" in result
        
        if result["success"] and result.get("assignments"):
            # All assignments should be for skilled nursing or compatible services
            for assignment in result["assignments"]:
                service_type = assignment.get("service_type", "")
                assert service_type in ["skilled_nursing", "general"]
    
    def test_constraint_satisfaction_tracking(self):
        """Test that constraint satisfaction is properly tracked."""
        result = self.job.run()
        
        if result["success"] and "constraint_summary" in result:
            constraint_summary = result["constraint_summary"]
            assert "satisfied_constraints" in constraint_summary
            assert "violated_constraints" in constraint_summary
            assert "constraint_violation_types" in constraint_summary
            
            # Counts should be non-negative
            assert constraint_summary["satisfied_constraints"] >= 0
            assert constraint_summary["violated_constraints"] >= 0
    
    def test_service_type_statistics(self):
        """Test that service type statistics are properly calculated."""
        result = self.job.run()
        
        if result["success"] and "service_type_statistics" in result:
            stats = result["service_type_statistics"]
            
            for service_type, service_stats in stats.items():
                assert "assigned" in service_stats
                assert "total" in service_stats
                assert "success_rate" in service_stats
                
                # Verify logical consistency
                assert service_stats["assigned"] <= service_stats["total"]
                assert 0 <= service_stats["success_rate"] <= 100
                
                # Success rate should match calculation
                if service_stats["total"] > 0:
                    expected_rate = (service_stats["assigned"] / service_stats["total"]) * 100
                    assert abs(service_stats["success_rate"] - expected_rate) < 0.1
    
    def test_error_handling(self):
        """Test error handling for invalid inputs."""
        # Test with invalid date (far in the past)
        old_date = date(2020, 1, 1)
        result = self.job.run(target_date=old_date)
        
        # Should still return a valid result structure
        assert isinstance(result, dict)
        assert "success" in result
        assert "processing_time" in result
    
    def test_empty_appointments_handling(self):
        """Test handling when no appointments are available."""
        # This test depends on the demo data, but we can test the structure
        result = self.job.run()
        
        assert isinstance(result, dict)
        assert "success" in result
        
        if result["success"] and result.get("summary", {}).get("total_appointments", 0) == 0:
            # Should handle empty case gracefully
            assert result["summary"]["assigned_appointments"] == 0
            assert result["summary"]["unassigned_appointments"] == 0
            assert result["summary"]["assignment_rate_percent"] == 0
