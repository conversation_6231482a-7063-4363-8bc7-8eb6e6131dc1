2025-06-24 21:16:59,722 - root - INFO - Logger initialized. Log file: D:\Work\Scheduler\appointment-scheduler\logs\assign_appointments_2025-06-24_21-16-59.log
2025-06-24 21:16:59,723 - __main__ - INFO - Initializing AssignAppointmentJob...
2025-06-24 21:16:59,728 - src.appointment_scheduler.config_manager - INFO - Loaded scheduler configuration from config\scheduler.yml
2025-06-24 21:16:59,731 - src.appointment_scheduler.config_manager - INFO - Loaded service config for skilled_nursing: config\skilled_nursing.yml
2025-06-24 21:16:59,736 - src.appointment_scheduler.config_manager - INFO - Loaded service config for physical_therapy: config\physical_therapy.yml
2025-06-24 21:17:06,300 - __main__ - INFO - AssignAppointment job initialized with solver config
2025-06-24 21:17:06,301 - __main__ - INFO - Running job for target date: 2025-06-24
2025-06-24 21:17:06,301 - __main__ - INFO - 🚀 === ASSIGNMENT JOB STARTED ===
2025-06-24 21:17:06,302 - __main__ - INFO - 📅 Target Date: 2025-06-24
2025-06-24 21:17:06,302 - __main__ - INFO - 🏥 Service Type: All Services
2025-06-24 21:17:06,302 - __main__ - INFO - === STAGE 1: Loading Data ===
2025-06-24 21:17:06,303 - src.appointment_scheduler.data_loader - INFO - 🚀 Starting data loading process...
2025-06-24 21:17:06,303 - src.appointment_scheduler.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-24 21:17:06,303 - src.appointment_scheduler.data_loader - INFO - Reading provider data from: data\providers.yml
2025-06-24 21:17:06,315 - src.appointment_scheduler.data_loader - INFO - ✅ Provider data loaded: 6 providers
2025-06-24 21:17:06,316 - src.appointment_scheduler.data_loader - INFO -    - Sarah Johnson, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-24 21:17:06,316 - src.appointment_scheduler.data_loader - INFO -    - Michael Chen, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-24 21:17:06,316 - src.appointment_scheduler.data_loader - INFO -    - Lisa Rodriguez, LPN (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-24 21:17:06,317 - src.appointment_scheduler.data_loader - INFO -    - David Wilson, LPN (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-24 21:17:06,317 - src.appointment_scheduler.data_loader - INFO -    - Maria Garcia, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-24 21:17:06,317 - src.appointment_scheduler.data_loader - INFO -    - James Thompson, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-24 21:17:06,318 - src.appointment_scheduler.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-24 21:17:06,318 - src.appointment_scheduler.data_loader - INFO - Reading consumer data from: data\consumers.yml
2025-06-24 21:17:06,337 - src.appointment_scheduler.data_loader - INFO - ✅ Consumer data loaded: 8 consumers
2025-06-24 21:17:06,338 - src.appointment_scheduler.data_loader - INFO -    - Margaret Smith - Episode: episode_001
2025-06-24 21:17:06,338 - src.appointment_scheduler.data_loader - INFO -    - Robert Johnson - Episode: episode_002
2025-06-24 21:17:06,339 - src.appointment_scheduler.data_loader - INFO -    - Carmen Rodriguez - Episode: episode_003
2025-06-24 21:17:06,339 - src.appointment_scheduler.data_loader - INFO -    - Jose Martinez - Episode: episode_004
2025-06-24 21:17:06,339 - src.appointment_scheduler.data_loader - INFO -    - Jennifer Williams - Episode: episode_005
2025-06-24 21:17:06,340 - src.appointment_scheduler.data_loader - INFO -    - Thomas Brown - Episode: episode_006
2025-06-24 21:17:06,340 - src.appointment_scheduler.data_loader - INFO -    - Patricia Davis - Episode: episode_007
2025-06-24 21:17:06,341 - src.appointment_scheduler.data_loader - INFO -    - Richard Miller - Episode: episode_008
2025-06-24 21:17:06,341 - src.appointment_scheduler.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-24 21:17:06,341 - src.appointment_scheduler.data_loader - INFO - Reading appointment data from: data\appointments.yml
2025-06-24 21:17:06,360 - src.appointment_scheduler.data_loader - INFO - ✅ Appointment data loaded: 8 appointments
2025-06-24 21:17:06,361 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT RN - 60min - 2025-06-24
2025-06-24 21:17:06,361 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular RN - 60min - 2025-06-25
2025-06-24 21:17:06,362 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT PT - 45min - 2025-06-24
2025-06-24 21:17:06,362 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular PT - 45min - 2025-06-25
2025-06-24 21:17:06,363 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT CNA - 30min - 2025-06-24
2025-06-24 21:17:06,363 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular CNA - 30min - 2025-06-25
2025-06-24 21:17:06,363 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT LPN - 30min - 2025-06-24
2025-06-24 21:17:06,364 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular LPN - 30min - 2025-06-25
2025-06-24 21:17:06,364 - src.appointment_scheduler.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-24 21:17:06,364 - src.appointment_scheduler.data_loader - INFO - 📊 Total records loaded:
2025-06-24 21:17:06,365 - src.appointment_scheduler.data_loader - INFO -    - Providers: 6
2025-06-24 21:17:06,365 - src.appointment_scheduler.data_loader - INFO -    - Consumers: 8
2025-06-24 21:17:06,365 - src.appointment_scheduler.data_loader - INFO -    - Appointments: 8
2025-06-24 21:17:06,366 - src.appointment_scheduler.data_loader - INFO - ✅ All data loaded successfully!
2025-06-24 21:17:06,366 - __main__ - INFO - 📊 Initial data loaded: 6 providers, 8 consumers, 8 appointments
2025-06-24 21:17:06,366 - __main__ - INFO - === STAGE 2A: No service type filter applied ===
2025-06-24 21:17:06,367 - __main__ - INFO - ✅ Using all 8 appointments
2025-06-24 21:17:06,367 - __main__ - INFO - === STAGE 2B: Filtering by Date Range ===
2025-06-24 21:17:06,367 - __main__ - INFO - 📅 Date range: 2025-06-24 to 2025-06-30
2025-06-24 21:17:06,368 - __main__ - INFO - ✅ Filtered to 8 appointments for date range
2025-06-24 21:17:06,369 - __main__ - INFO - === STAGE 3: Creating Planning Entities ===
2025-06-24 21:17:06,369 - __main__ - INFO - ✅ Created 8 planning entities
2025-06-24 21:17:06,371 - __main__ - INFO - === STAGE 4: Creating Available Dates ===
2025-06-24 21:17:06,371 - __main__ - INFO - ✅ Created 5 available dates: ['2025-06-24', '2025-06-25', '2025-06-26', '2025-06-27', '2025-06-30']
2025-06-24 21:17:06,371 - __main__ - INFO - === STAGE 5: Creating Optimization Solution ===
2025-06-24 21:17:06,372 - __main__ - INFO - ✅ Solution created with 8 assignments, 5 available dates
2025-06-24 21:17:06,372 - __main__ - INFO - === STAGE 6: Starting Optimization Solver ===
2025-06-24 21:17:06,372 - __main__ - INFO - 🔧 Starting solver with 8 assignments
2025-06-24 21:17:06,373 - __main__ - INFO - 🏥 No specific service type - using default constraints
2025-06-24 21:17:06,373 - __main__ - INFO - 🏥 Using default service config: skilled_nursing
2025-06-24 21:17:06,373 - __main__ - INFO - 🔧 Creating solver instance...
2025-06-24 21:17:06,474 - __main__ - INFO - ⏱️  Solver time limit: 10 seconds
2025-06-24 21:17:06,475 - __main__ - INFO - 🚀 Starting optimization process...
2025-06-24 21:17:06,475 - __main__ - INFO -    - Applying constraint rules...
2025-06-24 21:17:06,475 - __main__ - INFO -    - Balancing workload across providers...
2025-06-24 21:17:06,476 - __main__ - INFO -    - Optimizing geographic distribution...
2025-06-24 21:17:06,476 - __main__ - INFO -    - Considering patient preferences...
2025-06-24 21:17:06,665 - timefold.solver - INFO - Solving started: time spent (61), best score (0hard/0soft), environment mode (PHASE_ASSERT), move thread count (NONE), random (JDK with seed 0).
2025-06-24 21:17:06,670 - timefold.solver - INFO - Problem scale: entity count (8), variable count (16), approximate value count (11), approximate problem scale (6.560962 × 10^11).
2025-06-24 21:17:10,438 - timefold.solver - INFO - Construction Heuristic phase (0) ended: time spent (3837), best score (-6hard/-22soft), move evaluation speed (63/sec), step total (8).
2025-06-24 21:17:40,584 - timefold.solver - INFO - Local Search phase (1) ended: time spent (33984), best score (-6hard/-22soft), move evaluation speed (12/sec), step total (168).
2025-06-24 21:17:40,586 - timefold.solver - INFO - Solving ended: time spent (33985), best score (-6hard/-22soft), move evaluation speed (18/sec), phase total (2), environment mode (PHASE_ASSERT), move thread count (NONE).
2025-06-24 21:17:40,638 - __main__ - INFO - ✅ Solver completed successfully!
2025-06-24 21:17:40,639 - __main__ - INFO - 📊 Final score: -6hard/-22soft
2025-06-24 21:17:40,639 - __main__ - INFO - ✅ Optimization solver completed
2025-06-24 21:17:40,640 - __main__ - INFO - === STAGE 7: Processing Results ===
2025-06-24 21:17:40,640 - __main__ - INFO - 📊 Processing assignment results...
2025-06-24 21:17:40,640 - __main__ - INFO - 🔍 Analyzing assignment results...
2025-06-24 21:17:40,640 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:17:40,641 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:17:40,642 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:17:40,642 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:17:40,642 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Maria Garcia, CNA on 2025-06-24
2025-06-24 21:17:40,642 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Maria Garcia, CNA on 2025-06-24
2025-06-24 21:17:40,643 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Lisa Rodriguez, LPN on 2025-06-24
2025-06-24 21:17:40,643 - __main__ - INFO - ✅ ASSIGNED: Unknown -> Lisa Rodriguez, LPN on 2025-06-24
2025-06-24 21:17:40,643 - __main__ - INFO - 📈 Calculating assignment statistics...
2025-06-24 21:17:40,643 - __main__ - INFO - === ASSIGNMENT RESULTS SUMMARY ===
2025-06-24 21:17:40,643 - __main__ - INFO - 📊 Total appointments: 8
2025-06-24 21:17:40,644 - __main__ - INFO - ✅ Assigned: 8 (100.0%)
2025-06-24 21:17:40,644 - __main__ - INFO - ❌ Unassigned: 0
2025-06-24 21:17:40,644 - __main__ - INFO - ⏱️  Processing time: 34.34 seconds
2025-06-24 21:17:40,644 - __main__ - INFO - 📈 Final score: -6hard/-22soft
2025-06-24 21:17:40,645 - __main__ - INFO - === SERVICE TYPE STATISTICS ===
2025-06-24 21:17:40,645 - __main__ - INFO - 🏥 skilled_nursing: 2/2 (100.0%)
2025-06-24 21:17:40,645 - __main__ - INFO - 🏥 physical_therapy: 2/2 (100.0%)
2025-06-24 21:17:40,645 - __main__ - INFO - 🏥 personal_care: 2/2 (100.0%)
2025-06-24 21:17:40,646 - __main__ - INFO - 🏥 general: 2/2 (100.0%)
2025-06-24 21:17:40,646 - __main__ - INFO - === CONSTRAINT SUMMARY ===
2025-06-24 21:17:40,646 - __main__ - INFO - ✅ Satisfied constraints: 28
2025-06-24 21:17:40,647 - __main__ - INFO - ❌ Violated constraints: 4
2025-06-24 21:17:40,647 - __main__ - INFO -    ❌ required_provider_skills: 2
2025-06-24 21:17:40,647 - __main__ - INFO -    ❌ provider_role_match: 2
2025-06-24 21:17:40,647 - __main__ - INFO - 🎉 === ASSIGNMENT JOB COMPLETED ===
2025-06-24 21:17:40,648 - __main__ - INFO - ⏱️  Total processing time: 34.34 seconds
