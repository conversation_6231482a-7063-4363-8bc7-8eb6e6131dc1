"""
Route Optimization Constraints for Day Planning (C016)

This module adapts vehicle routing/route optimization constraints for the day plan stage.
Following the same pattern as other working constraints: for_each + join + filter.
"""

from datetime import datetime

from timefold.solver.score import ConstraintFactory, HardSoftScore, constraint_provider, Constraint, Joiners

from ..domain import TimeSlotAssignment, ScheduledAppointment, Provider
from .base_constraints import _calculate_travel_time_between_appointments, _calculate_distance
PROVIDER_CAPACITY = "providerCapacity"
BREAK_TIME_VIOLATION = "breakTimeViolation"
MINIMIZE_TRAVEL_TIME = "minimizeTravelTime"
GEOGRAPHIC_CLUSTERING = "geographicClustering"

@constraint_provider
def route_optimization_constraints(factory: ConstraintFactory):
    return [
        provider_capacity(factory),
        break_time_violation(factory),
        minimize_travel_time(factory),
        geographic_clustering(factory),
    ]

# --- Hard constraints ---
def provider_capacity(factory: ConstraintFactory) -> Constraint:
    """Providers must not exceed their daily capacity limits."""
    return (
        factory.for_each(TimeSlotAssignment)
        .join(TimeSlotAssignment,
              Joiners.equal(lambda a: a.scheduled_appointment.provider))
        .filter(lambda a, b: (a.id != b.id and 
                             a.time_slot is not None and 
                             b.time_slot is not None and
                             _exceeds_capacity(a, b)))
        .penalize(HardSoftScore.ONE_HARD, lambda a, b: _calculate_capacity_penalty(a, b))
        .as_constraint(PROVIDER_CAPACITY)
    )

def break_time_violation(factory: ConstraintFactory) -> Constraint:
    """Providers must have adequate breaks between appointments."""
    return (
        factory.for_each(TimeSlotAssignment)
        .join(TimeSlotAssignment,
              Joiners.equal(lambda a: a.scheduled_appointment.provider))
        .filter(lambda a, b: (a.id != b.id and 
                             a.time_slot is not None and 
                             b.time_slot is not None and
                             _has_insufficient_break_time(a, b)))
        .penalize(HardSoftScore.ONE_HARD, lambda a, b: _calculate_break_violation_minutes(a, b))
        .as_constraint(BREAK_TIME_VIOLATION)
    )

# --- Soft constraints ---
def minimize_travel_time(factory: ConstraintFactory) -> Constraint:
    """Minimize total travel time across all providers."""
    return (
        factory.for_each(TimeSlotAssignment)
        .join(TimeSlotAssignment,
              Joiners.equal(lambda a: a.scheduled_appointment.provider))
        .filter(lambda a, b: (a.id != b.id and 
                             a.time_slot is not None and 
                             b.time_slot is not None))
        .penalize(HardSoftScore.ONE_SOFT, lambda a, b: _travel_time_penalty(a, b))
        .as_constraint(MINIMIZE_TRAVEL_TIME)
    )

def geographic_clustering(factory: ConstraintFactory) -> Constraint:
    """Prefer geographic clustering of appointments for each provider."""
    return (
        factory.for_each(TimeSlotAssignment)
        .join(TimeSlotAssignment,
              Joiners.equal(lambda a: a.scheduled_appointment.provider))
        .filter(lambda a, b: (a.id != b.id and 
                             a.time_slot is not None and 
                             b.time_slot is not None and
                             _has_clustering_penalty(a, b)))
        .penalize(HardSoftScore.ONE_SOFT, lambda a, b: _calculate_clustering_penalty_for_pair(a, b))
        .as_constraint(GEOGRAPHIC_CLUSTERING)
    )

# --- Helper functions ---
def _exceeds_capacity(a: TimeSlotAssignment, b: TimeSlotAssignment) -> bool:
    """Check if provider exceeds capacity with these assignments."""
    # This is a simplified check - in a real implementation, you'd need to count all assignments
    # For now, we'll just check if both assignments have high task points
    points_a = getattr(a.scheduled_appointment.appointment_data, 'task_points', 1)
    points_b = getattr(b.scheduled_appointment.appointment_data, 'task_points', 1)
    return (points_a + points_b) > 20  # Simplified threshold

def _calculate_capacity_penalty(a: TimeSlotAssignment, b: TimeSlotAssignment) -> int:
    """Calculate capacity penalty for this pair of assignments."""
    points_a = getattr(a.scheduled_appointment.appointment_data, 'task_points', 1)
    points_b = getattr(b.scheduled_appointment.appointment_data, 'task_points', 1)
    return max(0, (points_a + points_b) - 20)

def _has_insufficient_break_time(a: TimeSlotAssignment, b: TimeSlotAssignment) -> bool:
    """Check if there's insufficient break time between appointments."""
    a_time_slot = getattr(a, 'time_slot', None)
    b_time_slot = getattr(b, 'time_slot', None)
    if a_time_slot is None or b_time_slot is None:
        return False
    end_a = getattr(a_time_slot, 'end_time', None)
    start_b = getattr(b_time_slot, 'start_time', None)
    if end_a is None or start_b is None or end_a >= start_b:
        return False
    gap = (datetime.combine(datetime.today(), start_b) -
           datetime.combine(datetime.today(), end_a)).total_seconds() / 60
    min_break = 15
    provider = getattr(getattr(a, 'scheduled_appointment', None), 'provider', None)
    if provider is not None and getattr(provider, 'capacity', None) is not None:
        min_break = getattr(provider.capacity, 'min_break_between_tasks', 15)
    return bool(gap < min_break)

def _calculate_break_violation_minutes(a: TimeSlotAssignment, b: TimeSlotAssignment) -> int:
    """Calculate break violation penalty in minutes."""
    a_time_slot = getattr(a, 'time_slot', None)
    b_time_slot = getattr(b, 'time_slot', None)
    end_a = getattr(a_time_slot, 'end_time', None) if a_time_slot else None
    start_b = getattr(b_time_slot, 'start_time', None) if b_time_slot else None
    if end_a is None or start_b is None:
        return 0
    gap = (datetime.combine(datetime.today(), start_b) -
           datetime.combine(datetime.today(), end_a)).total_seconds() / 60
    min_break = 15
    provider = getattr(getattr(a, 'scheduled_appointment', None), 'provider', None)
    if provider is not None and getattr(provider, 'capacity', None) is not None:
        min_break = getattr(provider.capacity, 'min_break_between_tasks', 15)
    return int(max(0, min_break - gap))

def _travel_time_penalty(a: TimeSlotAssignment, b: TimeSlotAssignment) -> int:
    """Calculate travel time penalty between two appointments."""
    appointment_data_a = getattr(a, 'scheduled_appointment', None)
    appointment_data_b = getattr(b, 'scheduled_appointment', None)
    if appointment_data_a is None or appointment_data_b is None:
        return 0
    return _calculate_travel_time_between_appointments(
        appointment_data_a.appointment_data,
        appointment_data_b.appointment_data
    )

def _has_clustering_penalty(a: TimeSlotAssignment, b: TimeSlotAssignment) -> bool:
    """Check if this pair contributes to clustering penalty."""
    # Only consider pairs that have time slots assigned
    if getattr(a, 'time_slot', None) is None or getattr(b, 'time_slot', None) is None:
        return False
    return True

def _calculate_clustering_penalty_for_pair(a: TimeSlotAssignment, b: TimeSlotAssignment) -> int:
    """Calculate clustering penalty for this pair of assignments."""
    appointment_data_a = getattr(a, 'scheduled_appointment', None)
    appointment_data_b = getattr(b, 'scheduled_appointment', None)
    if appointment_data_a is None or appointment_data_b is None:
        return 0
    
    loc1 = getattr(appointment_data_a.appointment_data, 'location', None)
    loc2 = getattr(appointment_data_b.appointment_data, 'location', None)
    if loc1 is not None and loc2 is not None:
        return int(_calculate_distance(loc1, loc2) * 1000)  # scale for penalty
    return 0 