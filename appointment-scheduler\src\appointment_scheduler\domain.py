"""
Domain models for healthcare scheduling optimization.

This module defines the core domain entities used in the 2-stage optimization process:
1. Assignment Solver: Assigns providers and dates to appointments
2. Day Plan Solver: Optimizes timing and routing within a day

The models are designed to be framework-agnostic and can work with any optimization solver.
"""

from dataclasses import dataclass, field
from datetime import date, datetime, time, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel


# Weekday constants for scheduling
MONDAY = "monday"
TUESDAY = "tuesday"
WEDNESDAY = "wednesday"
THURSDAY = "thursday"
FRIDAY = "friday"
SATURDAY = "saturday"
SUNDAY = "sunday"

# Weekday list for iteration
WEEKDAYS = [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY]


def weekday_from_int(weekday: int) -> str:
    """Convert weekday integer (0=Monday, 6=Sunday) to weekday string."""
    weekday_mapping = {
        0: MONDAY,
        1: TUESDAY,
        2: WEDNESDAY,
        3: THURSDAY,
        4: FRIDAY,
        5: SATURDAY,
        6: SUNDAY
    }
    return weekday_mapping[weekday]


class Location(BaseModel):
    """Geographic point location - maps to GEOMETRY(POINT, 4326) in PostgreSQL."""
    latitude: float                              # Y coordinate
    longitude: float                             # X coordinate
    address: Optional[str] = None                # Human-readable address
    city: Optional[str] = None                   # City name
    state: Optional[str] = None                  # State/province
    country: Optional[str] = None                # Country
    zip_code: Optional[str] = None               # Postal code


class Geofence(BaseModel):
    """Geofence with boundary data for optimization-time spatial calculations."""
    id: Optional[int] = None                     # Database ID reference
    name: Optional[str] = None                   # "Brooklyn Heights", "Unsafe Zone 1"
    boundary_wkt: Optional[str] = None           # WKT format: "POLYGON((-73.9961 40.6955, ...))"
    zone_type: str                               # "service" or "blackout"
    description: Optional[str] = None            # Human-readable description

    # Service geofence fields (when zone_type = "service")
    priority: Optional[int] = None               # Priority if overlapping (1=highest)

    # Blackout geofence fields (when zone_type = "blackout")
    reason: Optional[str] = None                 # "unsafe_area", "no_parking", "traffic_congestion"
    severity: str = "hard"                       # "hard", "soft", "time_based"


class DateSpecificProviderAvailability(BaseModel):
    """Date-specific provider availability override (e.g., half-day leave, late start)."""

    date: date                                      # Specific date
    available_start: Optional[time] = None          # Available from this time (None = not available)
    available_end: Optional[time] = None            # Available until this time (None = not available)
    reason: Optional[str] = None                    # "half_day_leave", "medical_appointment", "training"

    @property
    def is_full_day_off(self) -> bool:
        """Check if provider is completely unavailable on this date."""
        return self.available_start is None and self.available_end is None

    @property
    def is_partial_day(self) -> bool:
        """Check if provider has partial availability on this date."""
        return (self.available_start is not None and self.available_end is not None and
                self.available_start != time(0, 0) and self.available_end != time(23, 59))


class ShiftPattern(BaseModel):
    """Healthcare shift pattern definition."""

    shift_name: str                                 # "day_shift", "night_shift", "weekend_shift"
    shift_start: time                               # Shift start time
    shift_end: time                                 # Shift end time
    crosses_midnight: bool = False                  # True if shift spans midnight (e.g., 11 PM - 7 AM)
    shift_days: List[str] = []                  # Days this shift applies to

    @property
    def duration_hours(self) -> float:
        """Calculate shift duration in hours."""
        if self.crosses_midnight:
            # Handle shifts that cross midnight
            start_minutes = self.shift_start.hour * 60 + self.shift_start.minute
            end_minutes = self.shift_end.hour * 60 + self.shift_end.minute
            if end_minutes < start_minutes:
                end_minutes += 24 * 60  # Add 24 hours
            return (end_minutes - start_minutes) / 60.0
        else:
            # Normal shift within same day
            start_minutes = self.shift_start.hour * 60 + self.shift_start.minute
            end_minutes = self.shift_end.hour * 60 + self.shift_end.minute
            return (end_minutes - start_minutes) / 60.0


class ProviderAvailability(BaseModel):
    """Provider availability schedule - healthcare shift-based model."""

    # Primary shift assignment
    primary_shift: Optional[ShiftPattern] = None    # Provider's main shift pattern

    # Additional shifts (for providers who work multiple shift types)
    additional_shifts: List[ShiftPattern] = []      # Extra shifts (overtime, coverage)

    # Working days - days provider is scheduled to work
    working_days: List[str] = [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY]

    # Standard break periods - applies to all working days
    break_periods: List[tuple[time, time]] = [
        (time(12, 0), time(13, 0)),  # Standard lunch break 12-1 PM
    ]

    # Split shifts for providers who work multiple shifts per day
    split_shifts: List[tuple[time, time]] = []  # [(start1, end1), (start2, end2), ...]

    # Working hours for providers without defined shifts (simple hour ranges)
    working_hours: Optional[tuple[time, time]] = None  # (start_time, end_time) for simple hour ranges

    # Holiday dates when provider is not available
    holidays: List[date] = []  # List of holiday dates

    # Date-specific availability overrides (half-day leave, appointments, etc.)
    date_specific_availability: List[DateSpecificProviderAvailability] = []

    # Time off periods - full days when provider is unavailable (vacation, sick days)
    time_off_periods: List[tuple[date, date]] = []  # [(start_date, end_date), ...]

    # Organizational constraints
    max_hours_per_day: int = 8                      # Standard organizational limit
    max_hours_per_week: int = 40                    # Standard organizational limit

    # Additional scheduling constraints for timeslot validation
    unavailable_time_slots: List[tuple[time, time]] = []  # Specific time slots when provider is unavailable
    max_appointment_duration_min: Optional[int] = None    # Maximum appointment duration in minutes

    # Operational constraints
    min_gap_between_assignments: int = 30           # Travel/prep time in minutes

    # Overtime policies
    overtime_allowed: bool = False
    max_overtime_hours_per_week: Optional[int] = None

    # On-call availability
    on_call_available: bool = False
    on_call_days: List[str] = []

    # Extension point
    properties: Dict[str, Any] = {}

    def get_shift_hours(self, specific_date: date) -> Optional[tuple[time, time]]:
        """Get shift hours for a specific date, considering overrides."""
        # Check for date-specific availability override
        for override in self.date_specific_availability:
            if override.date == specific_date:
                if override.is_full_day_off:
                    return None
                elif override.is_partial_day:
                    # Ensure both start and end times are not None before returning
                    if override.available_start is not None and override.available_end is not None:
                        return (override.available_start, override.available_end)
                    else:
                        # If either time is None, treat as not available
                        return None
                else:
                    # Full day available
                    return self._get_default_shift_hours(specific_date.weekday())

        # Check if date is in time off period
        for start_date, end_date in self.time_off_periods:
            if start_date <= specific_date <= end_date:
                return None

        # Use default shift hours for the weekday
        weekday = specific_date.weekday()
        return self._get_default_shift_hours(weekday)

    def _get_default_shift_hours(self, weekday: int) -> Optional[tuple[time, time]]:
        """Get default shift hours for a weekday."""
        weekday_enum = weekday_from_int(weekday)
        
        # Check primary shift first
        if self.primary_shift and weekday_enum in self.primary_shift.shift_days:
            return (self.primary_shift.shift_start, self.primary_shift.shift_end)
        
        # Check additional shifts
        for shift in self.additional_shifts:
            if weekday_enum in shift.shift_days:
                return (shift.shift_start, shift.shift_end)
        
        # Check if provider has working days defined for this weekday
        if weekday_enum in self.working_days:
            # If working_hours are defined, use them
            if self.working_hours:
                return self.working_hours
            
            # If split_shifts are defined, return the first one (or could be enhanced to return all)
            if self.split_shifts:
                return self.split_shifts[0]  # Return first split shift as default
        
        # Default: not available
        return None

    def is_available_at_time(self, specific_datetime: datetime) -> bool:
        """Check if provider is available at a specific datetime."""
        specific_date = specific_datetime.date()
        current_time = specific_datetime.time()
        
        # Get shift hours for the date
        shift_hours = self.get_shift_hours(specific_date)
        if not shift_hours:
            return False
        
        start_time, end_time = shift_hours
        
        # Check if time falls within shift hours
        if self._shift_crosses_midnight(start_time, end_time):
            # Time is available if it's after start OR before end
            time_available = current_time >= start_time or current_time <= end_time
        else:
            # Normal shift within same day
            time_available = start_time <= current_time <= end_time

        if not time_available:
            return False

        # Check if time falls within break periods
        for break_start, break_end in self.break_periods:
            if break_start <= current_time <= break_end:
                return False

        return True

    def _shift_crosses_midnight(self, start_time: time, end_time: time) -> bool:
        """Check if shift crosses midnight."""
        return start_time > end_time

    def is_time_within_split_shifts(self, check_time: time) -> bool:
        """Check if a time falls within any split shift."""
        if not self.split_shifts:
            return False
        
        for shift_start, shift_end in self.split_shifts:
            if shift_start <= shift_end:  # Normal shift
                if shift_start <= check_time < shift_end:
                    return True
            else:  # Overnight shift
                if check_time >= shift_start or check_time <= shift_end:
                    return True
        
        return False

    def get_all_available_shifts_for_day(self, weekday: int) -> List[tuple[time, time]]:
        """Get all available shifts for a specific weekday."""
        weekday_enum = weekday_from_int(weekday)
        available_shifts = []
        
        # Check primary shift
        if self.primary_shift and weekday_enum in self.primary_shift.shift_days:
            available_shifts.append((self.primary_shift.shift_start, self.primary_shift.shift_end))
        
        # Check additional shifts
        for shift in self.additional_shifts:
            if weekday_enum in shift.shift_days:
                available_shifts.append((shift.shift_start, shift.shift_end))
        
        # Check working hours (if no shifts defined)
        if weekday_enum in self.working_days and self.working_hours and not available_shifts:
            available_shifts.append(self.working_hours)
        
        # Add split shifts
        if weekday_enum in self.working_days and self.split_shifts:
            available_shifts.extend(self.split_shifts)
        
        return available_shifts


class ProviderCapacity(BaseModel):
    """Daily workload limits and capacity constraints for healthcare providers."""

    # Daily workload limits (required for optimization)
    max_allocated_task_points_in_day: int = 27             # Max complexity points per day
    max_tasks_count_in_day: int = 6                        # Max number of tasks per day
    max_hours_per_day: int = 8                             # Maximum working hours per day

    # Additional capacity constraints
    max_consecutive_tasks: int = 4                         # Max tasks without break
    min_break_between_tasks: int = 15                      # Minimum minutes between tasks

    # Extension point for capacity-specific attributes
    properties: Dict[str, Any] = {}


class ProviderPreferences(BaseModel):
    """Provider preferences and restrictions for assignment optimization."""

    # Consumer preferences
    blacklisted_consumers: List[str] = []                  # Specific patients provider won't serve
    preferred_consumers: List[str] = []                    # Specific patients provider prefers

    # Geographic preferences
    blackout_areas: List[str] = []                         # Geographic areas to avoid

    # Task preferences
    preferred_task_types: List[str] = []                   # Task types provider prefers
    blacklisted_task_types: List[str] = []                 # Task types provider won't do

    # Extension point for preference-specific attributes
    properties: Dict[str, Any] = {}


class ConsumerPreferences(BaseModel):
    """Consumer preferences for healthcare provider assignment."""

    # Scheduling preferences
    preferred_days: List[str] = []                     # Days consumer prefers to receive care
    preferred_hours: Optional[tuple[time, time]] = None    # Time window consumer prefers (start, end)
    unavailable_days: List[str] = []                   # Days consumer is unavailable
    unavailable_hours: List[tuple[time, time]] = []        # Time windows when consumer is unavailable

    # Cultural and language preferences
    cultural_considerations: List[str] = []                # Cultural needs/preferences
    language: Optional[str] = None                         # Preferred language (no default assumption)
    gender_preference: Optional[str] = None                # Preferred provider gender

    # Provider preferences
    preferred_providers: List[str] = []                    # Specific providers consumer prefers

    # Extension point for preference-specific attributes
    properties: Dict[str, Any] = {}


@dataclass
class Provider:
    """Healthcare provider or service provider."""
    id: UUID
    name: str

    # ALL properties promoted to first-class fields for maximum type safety
    home_location: Optional[Location] = None        # Provider's home base - GEOMETRY(POINT, 4326)
    service_areas: List[Geofence] = field(default_factory=list)              # Service area geofences only (structured objects)
    languages: List[str] = field(default_factory=list)
    transportation: Optional[str] = None
    availability: Optional[ProviderAvailability] = None     # Comprehensive availability schedule
    current_task_count: int = 0
    critical: bool = False

    # Real-time availability status
    current_availability_status: str = "AVAILABLE"  # "AVAILABLE", "UNAVAILABLE", "BUSY", "OFF_DUTY"
    current_unavailable_until: Optional[datetime] = None  # When provider becomes available again

    # Healthcare role specification
    role: Optional[str] = None                      # "RN", "LPN", "CNA", "PT", "OT"
    skills: List[str] = field(default_factory=list)

    # Daily workload limits (structured object)
    capacity: ProviderCapacity = field(default_factory=ProviderCapacity) # Structured capacity constraints

    # Provider preferences (structured object)
    provider_preferences: ProviderPreferences = field(default_factory=ProviderPreferences)

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Consumer:
    """Patient or service consumer."""
    id: UUID
    name: str

    # Core consumer data
    location: Optional[Location] = None
    care_episode_id: Optional[str] = None

    # All consumer preferences consolidated into structured object
    consumer_preferences: ConsumerPreferences = field(default_factory=ConsumerPreferences)

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = field(default_factory=dict)


class AppointmentStatus(str, Enum):
    """Appointment status tracking through optimization stages."""
    PENDING_TO_ASSIGN = "PENDING_TO_ASSIGN"        # Initial state for new appointments
    ASSIGNED = "ASSIGNED"                          # After assignment solver (provider + date assigned)
    MANUAL_ASSIGNMENT = "MANUAL_ASSIGNMENT"        # User selected alternative assignment
    WAITING_FOR_DAYPLANNER = "WAITING_FOR_DAYPLANNER"  # Sent to day planner
    PLAN_DONE = "PLAN_DONE"                        # Day planning completed
    PENDING_PROVIDER_CONFIRMATION = "PENDING_PROVIDER_CONFIRMATION"  # Awaiting provider confirmation
    PROVIDER_CONFIRMED = "PROVIDER_CONFIRMED"      # Provider confirmed their daily plan
    ROUTE_OPTIMIZED = "ROUTE_OPTIMIZED"            # Route optimization completed
    IN_PROGRESS = "IN_PROGRESS"                    # Provider started the visit
    COMPLETED = "COMPLETED"                        # Visit completed
    CANCELLED = "CANCELLED"                        # Appointment cancelled


class AppointmentPinning(BaseModel):
    """Appointment pinning management for real-time planning."""

    # Pinning flags (prevent re-optimization of completed/in-progress visits)
    is_pinned: bool = False                      # General pinning flag
    pin_provider: bool = False                   # Pin assigned provider (prevent provider reassignment)
    pin_date: bool = False                       # Pin assigned date (prevent date changes)
    pin_time: bool = False                       # Pin assigned time (prevent time changes)
    pin_reason: Optional[str] = None             # Reason for pinning (e.g., "in_progress", "completed", "patient_request")

    def pin_assignment(self, reason: str = "completed") -> None:
        """Pin the entire assignment (provider, date, time) to prevent re-optimization."""
        self.is_pinned = True
        self.pin_provider = True
        self.pin_date = True
        self.pin_time = True
        self.pin_reason = reason

    def pin_provider_assignment(self, reason: str = "patient_preference") -> None:
        """Pin only the provider assignment, allowing date/time changes."""
        self.pin_provider = True
        self.pin_reason = reason

    def pin_date_assignment(self, reason: str = "patient_request") -> None:
        """Pin only the date assignment, allowing provider/time changes."""
        self.pin_date = True
        self.pin_reason = reason

    def pin_time_assignment(self, reason: str = "in_progress") -> None:
        """Pin only the time assignment, allowing provider/date changes."""
        self.pin_time = True
        self.pin_reason = reason

    def unpin_assignment(self) -> None:
        """Remove all pinning constraints (alias for unpin_all)."""
        self.unpin_all()

    def unpin_all(self) -> None:
        """Remove all pinning constraints."""
        self.is_pinned = False
        self.pin_provider = False
        self.pin_date = False
        self.pin_time = False
        self.pin_reason = None

    def is_any_pinned(self) -> bool:
        """Check if any aspect of the appointment is pinned."""
        return self.is_pinned or self.pin_provider or self.pin_date or self.pin_time

    def get_pinning_status(self) -> Dict[str, Any]:
        """Get detailed pinning status for debugging/monitoring."""
        return {
            "is_pinned": self.is_pinned,
            "pin_provider": self.pin_provider,
            "pin_date": self.pin_date,
            "pin_time": self.pin_time,
            "pin_reason": self.pin_reason,
            "pinned_aspects": [
                aspect for aspect, pinned in [
                    ("provider", self.pin_provider),
                    ("date", self.pin_date),
                    ("time", self.pin_time)
                ] if pinned
            ]
        }


class AppointmentTiming(BaseModel):
    """Appointment timing constraints and preferences."""

    # Timing constraints - program_type defines behavior
    is_timed_visit: bool = False                 # True for appointments with specific times (e.g., medication at 8 AM)
    preferred_time: Optional[time] = None        # Specific time preference for timed visits
    time_flexibility_minutes: int = 15           # How much the time can vary (±15 minutes)
    earliest_start: Optional[datetime] = None    # Earliest possible start time
    latest_end: Optional[datetime] = None        # Latest possible end time

    def is_flexible(self) -> bool:
        """Check if appointment has flexible timing."""
        return not self.is_timed_visit and self.time_flexibility_minutes > 0

    def get_time_window(self, base_time: datetime) -> tuple[datetime, datetime]:
        """Get the acceptable time window around a base time."""
        if self.is_timed_visit and self.preferred_time:
            # For timed visits, use preferred time with flexibility
            preferred_datetime = datetime.combine(base_time.date(), self.preferred_time)
            flexibility = timedelta(minutes=self.time_flexibility_minutes)
            return (preferred_datetime - flexibility, preferred_datetime + flexibility)
        else:
            # For flexible visits, use flexibility around base time
            flexibility = timedelta(minutes=self.time_flexibility_minutes)
            return (base_time - flexibility, base_time + flexibility)


class AppointmentRelationships(BaseModel):
    """Appointment relationships and dependencies."""

    # Relationship fields for episode grouping and dependencies
    care_episode_id: Optional[str] = None        # For grouping related appointments
    related_appointment_ids: List[str] = []      # Generic grouping mechanism (using strings for flexibility)
    prerequisite_appointment_ids: List[str] = [] # Dependencies (using strings for flexibility)
    sequence_order: Optional[int] = None         # Order within group
    same_provider_required: bool = False         # Continuity requirement

    def has_dependencies(self) -> bool:
        """Check if appointment has prerequisite dependencies."""
        return len(self.prerequisite_appointment_ids) > 0

    def has_relationships(self) -> bool:
        """Check if appointment is part of a care episode or has relationships."""
        return (self.care_episode_id is not None or
                len(self.related_appointment_ids) > 0 or
                self.has_dependencies())

    def is_part_of_sequence(self) -> bool:
        """Check if appointment is part of an ordered sequence."""
        return self.sequence_order is not None


# Base appointment data (shared between stages)
@dataclass
class AppointmentData:
    """Base appointment data shared between assignment and dayplan stages."""
    id: UUID
    consumer_id: UUID
    appointment_date: date
    required_skills: list[str]
    duration_min: int
    urgent: bool = False
    active: bool = True
    status: str = "PENDING_TO_ASSIGN"
    location: Optional[Location] = None
    priority: str = "normal"
    task_points: Optional[int] = None
    required_role: Optional[str] = None
    timing: AppointmentTiming = field(default_factory=AppointmentTiming)
    relationships: AppointmentRelationships = field(default_factory=AppointmentRelationships)
    pinning: AppointmentPinning = field(default_factory=AppointmentPinning)
    properties: dict = field(default_factory=dict)

    def is_flexible_timing(self) -> bool:
        """Check if appointment has flexible timing."""
        return self.timing.is_flexible()

    def get_time_window(self, base_time: datetime) -> tuple[datetime, datetime]:
        """Get the acceptable time window around a base time."""
        return self.timing.get_time_window(base_time)

    def has_dependencies(self) -> bool:
        """Check if appointment has prerequisite dependencies."""
        return self.relationships.has_dependencies()

    def has_relationships(self) -> bool:
        """Check if appointment is part of a care episode or has relationships."""
        return self.relationships.has_relationships()

    def is_any_pinned(self) -> bool:
        """Check if any aspect of the appointment is pinned."""
        return self.pinning.is_any_pinned()

    def get_pinning_status(self) -> Dict[str, Any]:
        """Get detailed pinning status for debugging/monitoring."""
        return self.pinning.get_pinning_status()


# Framework-agnostic domain classes (no Timefold annotations)
@dataclass
class AppointmentAssignment:
    """Planning entity representing the assignment of an appointment to a provider and date."""
    id: str
    appointment_data: AppointmentData
    provider: Optional[Provider] = field(default=None)
    assigned_date: Optional[date] = field(default=None)
    
    def __str__(self):
        if self.provider is None or self.assigned_date is None:
            return f"{self.appointment_data.id} -> unassigned"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}"


@dataclass
class ScheduledAppointment:
    """Appointment that has been assigned a date and provider but needs time assignment."""
    id: str
    appointment_data: AppointmentData
    provider: Provider
    assigned_date: date
    assigned_time: Optional[time] = None  # Will be assigned by DayPlan job
    
    def __str__(self):
        time_str = f" at {self.assigned_time}" if self.assigned_time else " (time TBD)"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}{time_str}"


@dataclass
class TimeSlotAssignment:
    """Planning entity for assigning time slots to scheduled appointments."""
    id: str
    scheduled_appointment: ScheduledAppointment
    time_slot: Optional[time] = field(default=None)
    
    def __str__(self):
        if self.time_slot is None:
            return f"{self.scheduled_appointment} -> no time assigned"
        return f"{self.scheduled_appointment.appointment_data.id} -> {self.scheduled_appointment.provider.name} at {self.time_slot}"


@dataclass
class AppointmentSchedule:
    """Solution representing the complete appointment schedule."""
    id: str
    providers: List[Provider]
    available_dates: List[date]
    appointment_assignments: List[AppointmentAssignment]
    score: Optional[Any] = field(default=None)  # Generic score type
    
    def get_provider_assignments(self, provider: Provider) -> List[AppointmentAssignment]:
        """Get all appointments assigned to a specific provider."""
        return [assignment for assignment in self.appointment_assignments 
                if assignment.provider == provider]
    
    def get_provider_daily_workload(self, provider: Provider, target_date: date) -> int:
        """Calculate provider's workload for a specific date."""
        assignments = self.get_provider_assignments(provider)
        return len([a for a in assignments if a.assigned_date == target_date])


@dataclass
class DaySchedule:
    """Solution representing the daily time slot assignments."""
    id: str
    date: date
    time_slots: List[time]
    scheduled_appointments: List[ScheduledAppointment]
    time_assignments: List[TimeSlotAssignment]
    score: Optional[Any] = field(default=None)  # Generic score type


# Configuration models
class ServiceConfig(BaseModel):
    """Configuration for a specific service type."""
    service_type: str
    required_skills: List[str]
    geographic_radius_miles: float = 25.0
    max_daily_appointments_per_provider: int = 8
    max_weekly_hours_per_provider: int = 40
    continuity_weight: float = 0.8
    workload_balance_weight: float = 0.6
    geographic_clustering_weight: float = 0.4
    patient_preference_weight: float = 0.7
    capacity_threshold_percentage: float = 0.9


class SchedulerConfig(BaseModel):
    """Main scheduler configuration."""
    rolling_window_days: int = 7
    batch_size: int = 100
    max_solving_time_seconds: int = 300
    config_folder: str = "config"
    log_level: str = "INFO"
    enable_geographic_clustering: bool = True
    enable_continuity_of_care: bool = True
    enable_workload_balancing: bool = True


class GeographicCluster(BaseModel):
    """Geographic cluster for route efficiency."""
    center_latitude: float
    center_longitude: float
    radius_miles: float
    provider_ids: List[str]
    patient_ids: List[str]


class AssignmentResult(BaseModel):
    """Result of appointment assignment."""
    appointment_id: str
    patient_id: str
    provider_id: str
    time_slot_id: str
    score: float
    constraints_satisfied: List[str]
    constraints_violated: List[str]
    assignment_date: datetime = field(default_factory=datetime.now)


class BatchAssignmentResult(BaseModel):
    """Result of batch appointment assignment."""
    batch_id: str
    total_appointments: int
    assigned_appointments: int
    unassigned_appointments: int
    average_score: float
    processing_time_seconds: float
    results: List[AssignmentResult]
    created_at: datetime = field(default_factory=datetime.now) 