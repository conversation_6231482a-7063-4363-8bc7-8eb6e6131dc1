"""
Demo data generation for healthcare scheduling optimization.

This module provides realistic demo data for testing the 2-stage optimization system:
1. Assignment Solver: Assigns providers and dates to appointments
2. Day Plan Solver: Optimizes timing and routing within a day
"""

import random
from datetime import date, datetime, time, timedelta
from typing import List, Dict, Any
from uuid import uuid4

from .domain import (
    Provider, Consumer, AppointmentData, Location,
    ProviderAvailability, ShiftPattern, ProviderCapacity, ProviderPreferences,
    ConsumerPreferences, AppointmentTiming, AppointmentRelationships, AppointmentPinning,
    ScheduledAppointment, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY
)


def create_demo_providers() -> List[Provider]:
    """Create demo providers with realistic healthcare roles and skills."""
    
    providers = [
        # RNs
        Provider(
            id=uuid4(),
            name="Sarah Johnson, RN",
            home_location=Location(
                latitude=40.7128, 
                longitude=-74.0060, 
                city="New York", 
                state="NY",
                address="555 Broadway, New York, NY 10012"
            ),
            languages=["English", "Spanish"],
            transportation="car",
            role="RN",
            skills=["medication_management", "wound_care", "assessment", "iv_therapy"],
            # working_days=[Weekday.MONDAY, Weekday.TUESDAY, Weekday.WEDNESDAY, Weekday.THURSDAY, Weekday.FRIDAY],
            # max_hours_per_day=8,
            # max_hours_per_week=40,
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=27,
                max_tasks_count_in_day=6,
                max_hours_per_day=8,
                max_consecutive_tasks=4,
                min_break_between_tasks=15
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["medication_management", "wound_care", "assessment"],
                blacklisted_task_types=["heavy_lifting"],
                preferred_consumers=["elderly", "chronic_conditions"]
            )
        ),
        Provider(
            id=uuid4(),
            name="Michael Chen, RN",
            home_location=Location(
                latitude=40.7589, 
                longitude=-73.9851, 
                city="New York", 
                state="NY",
                address="777 5th Avenue, New York, NY 10022"
            ),
            languages=["English", "Mandarin"],
            transportation="car",
            role="RN",
            skills=["medication_management", "wound_care", "assessment", "diabetes_management"],
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=27,
                max_tasks_count_in_day=6,
                max_hours_per_day=8,
                max_consecutive_tasks=4,
                min_break_between_tasks=15
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["medication_management", "wound_care", "assessment"],
                blacklisted_task_types=["heavy_lifting"],
                preferred_consumers=["elderly", "chronic_conditions"]
            )
        ),
        
        # LPNs
        Provider(
            id=uuid4(),
            name="Lisa Rodriguez, LPN",
            home_location=Location(
                latitude=40.7505, 
                longitude=-73.9934, 
                city="New York", 
                state="NY",
                address="888 Madison Avenue, New York, NY 10021"
            ),
            languages=["English", "Spanish"],
            transportation="car",
            role="LPN",
            skills=["medication_administration", "vital_signs", "basic_care", "catheter_care"],
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=24,
                max_tasks_count_in_day=8,
                max_hours_per_day=8,
                max_consecutive_tasks=5,
                min_break_between_tasks=10
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["medication_administration", "vital_signs", "basic_care"],
                preferred_consumers=["stable_patients"]
            )
        ),
        Provider(
            id=uuid4(),
            name="David Wilson, LPN",
            home_location=Location(
                latitude=40.7549, 
                longitude=-73.9840, 
                city="New York", 
                state="NY",
                address="999 Park Avenue, New York, NY 10021"
            ),
            languages=["English"],
            transportation="car",
            role="LPN",
            skills=["medication_administration", "vital_signs", "basic_care", "ostomy_care"],
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=24,
                max_tasks_count_in_day=8,
                max_hours_per_day=8,
                max_consecutive_tasks=5,
                min_break_between_tasks=10
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["medication_administration", "vital_signs", "basic_care"],
                preferred_consumers=["stable_patients"]
            )
        ),
        
        # CNAs
        Provider(
            id=uuid4(),
            name="Maria Garcia, CNA",
            home_location=Location(
                latitude=40.7484, 
                longitude=-73.9857, 
                city="New York", 
                state="NY",
                address="111 Lexington Avenue, New York, NY 10016"
            ),
            languages=["English", "Spanish"],
            transportation="car",
            role="CNA",
            skills=["personal_care", "mobility_assistance", "housekeeping", "meal_assistance"],
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=20,
                max_tasks_count_in_day=10,
                max_hours_per_day=8,
                max_consecutive_tasks=6,
                min_break_between_tasks=10
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["personal_care", "mobility_assistance", "housekeeping"],
                preferred_consumers=["mobility_limited"]
            )
        ),
        Provider(
            id=uuid4(),
            name="James Thompson, CNA",
            home_location=Location(
                latitude=40.7527, 
                longitude=-73.9772, 
                city="New York", 
                state="NY",
                address="222 3rd Avenue, New York, NY 10003"
            ),
            languages=["English"],
            transportation="car",
            role="CNA",
            skills=["personal_care", "mobility_assistance", "housekeeping", "companionship"],
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=20,
                max_tasks_count_in_day=10,
                max_hours_per_day=8,
                max_consecutive_tasks=6,
                min_break_between_tasks=10
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["personal_care", "mobility_assistance", "housekeeping"],
                preferred_consumers=["mobility_limited"]
            )
        ),
        
        # Physical Therapist
        Provider(
            id=uuid4(),
            name="Dr. Emily Davis, PT",
            home_location=Location(
                latitude=40.7562, 
                longitude=-73.9870, 
                city="New York", 
                state="NY",
                address="333 2nd Avenue, New York, NY 10003"
            ),
            languages=["English"],
            transportation="car",
            role="PT",
            skills=["physical_therapy", "mobility_training", "strength_training", "rehabilitation"],
            capacity=ProviderCapacity(
                max_allocated_task_points_in_day=30,
                max_tasks_count_in_day=4,
                max_hours_per_day=8,
                max_consecutive_tasks=3,
                min_break_between_tasks=20
            ),
            provider_preferences=ProviderPreferences(
                preferred_task_types=["physical_therapy", "mobility_training", "strength_training"],
                preferred_consumers=["rehabilitation", "post_surgery"]
            )
        ),
    ]
    
    return providers


def create_demo_consumers() -> List[Consumer]:
    """Create realistic demo consumers (patients) with comprehensive preferences."""
    
    # Create consumer preferences
    elderly_preferences = ConsumerPreferences(
        preferred_days=[MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY],
        preferred_hours=(time(9, 0), time(15, 0)),
        unavailable_hours=[(time(12, 0), time(13, 0))],  # Lunch time
        language="English",
        cultural_considerations=["respect_elderly", "gentle_approach"]
    )
    
    spanish_speaking_preferences = ConsumerPreferences(
        preferred_days=[MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY],
        preferred_hours=(time(10, 0), time(16, 0)),
        language="Spanish",
        cultural_considerations=["family_present", "traditional_values"]
    )
    
    rehabilitation_preferences = ConsumerPreferences(
        preferred_days=[MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY],
        preferred_hours=(time(8, 0), time(14, 0)),
        language="English",
        cultural_considerations=["motivation_focused", "goal_oriented"]
    )
    
    chronic_condition_preferences = ConsumerPreferences(
        preferred_days=[MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY],
        preferred_hours=(time(9, 0), time(17, 0)),
        language="English",
        cultural_considerations=["consistent_care", "medication_focused"]
    )
    
    consumers = [
        # Elderly patients
        Consumer(
            id=uuid4(),
            name="Margaret Smith",
            location=Location(
                latitude=40.7200, 
                longitude=-74.0000, 
                city="New York", 
                state="NY",
                address="123 Park Avenue, Apt 4B, New York, NY 10016"
            ),
            care_episode_id="episode_001",
            consumer_preferences=elderly_preferences
        ),
        Consumer(
            id=uuid4(),
            name="Robert Johnson",
            location=Location(
                latitude=40.7300, 
                longitude=-74.0100, 
                city="New York", 
                state="NY",
                address="456 Madison Avenue, Suite 8, New York, NY 10022"
            ),
            care_episode_id="episode_002",
            consumer_preferences=elderly_preferences
        ),
        
        # Spanish-speaking patients
        Consumer(
            id=uuid4(),
            name="Carmen Rodriguez",
            location=Location(
                latitude=40.7400, 
                longitude=-74.0200, 
                city="New York", 
                state="NY",
                address="789 5th Avenue, Apt 12C, New York, NY 10065"
            ),
            care_episode_id="episode_003",
            consumer_preferences=spanish_speaking_preferences
        ),
        Consumer(
            id=uuid4(),
            name="Jose Martinez",
            location=Location(
                latitude=40.7500, 
                longitude=-74.0300, 
                city="New York", 
                state="NY",
                address="321 Lexington Avenue, Floor 15, New York, NY 10016"
            ),
            care_episode_id="episode_004",
            consumer_preferences=spanish_speaking_preferences
        ),
        
        # Rehabilitation patients
        Consumer(
            id=uuid4(),
            name="Jennifer Williams",
            location=Location(
                latitude=40.7600, 
                longitude=-74.0400, 
                city="New York", 
                state="NY",
                address="654 3rd Avenue, Apt 7D, New York, NY 10017"
            ),
            care_episode_id="episode_005",
            consumer_preferences=rehabilitation_preferences
        ),
        Consumer(
            id=uuid4(),
            name="Thomas Brown",
            location=Location(
                latitude=40.7700, 
                longitude=-74.0500, 
                city="New York", 
                state="NY",
                address="987 2nd Avenue, Suite 22, New York, NY 10022"
            ),
            care_episode_id="episode_006",
            consumer_preferences=rehabilitation_preferences
        ),
        
        # Chronic condition patients
        Consumer(
            id=uuid4(),
            name="Patricia Davis",
            location=Location(
                latitude=40.7800, 
                longitude=-74.0600, 
                city="New York", 
                state="NY",
                address="147 1st Avenue, Apt 3A, New York, NY 10003"
            ),
            care_episode_id="episode_007",
            consumer_preferences=chronic_condition_preferences
        ),
        Consumer(
            id=uuid4(),
            name="Richard Miller",
            location=Location(
                latitude=40.7900, 
                longitude=-74.0700, 
                city="New York", 
                state="NY",
                address="258 York Avenue, Floor 9, New York, NY 10028"
            ),
            care_episode_id="episode_008",
            consumer_preferences=chronic_condition_preferences
        )
    ]
    
    return consumers


def create_demo_appointments(consumers: List[Consumer]) -> List[AppointmentData]:
    """Create demo appointments with realistic healthcare scheduling data."""
    
    appointments = []
    appointment_id = 0
    
    # Create a smaller set of appointments for testing
    for i, consumer in enumerate(consumers[:4]):  # Only use first 4 consumers
        # Create 2-3 appointments per consumer
        for j in range(2):
            appointment_id += 1
            
            # Determine service type based on consumer index
            if i == 0:  # First consumer - skilled nursing
                required_skills = ["medication_management", "wound_care"]
                required_role = "RN"
                duration_min = 60
                is_timed = False
            elif i == 1:  # Second consumer - physical therapy
                required_skills = ["physical_therapy", "mobility_training"]
                required_role = "PT"
                duration_min = 45
                is_timed = True
            elif i == 2:  # Third consumer - personal care
                required_skills = ["personal_care", "mobility_assistance"]
                required_role = "CNA"
                duration_min = 30
                is_timed = False
            else:  # Fourth consumer - medication administration
                required_skills = ["medication_administration", "vital_signs"]
                required_role = "LPN"
                duration_min = 30
                is_timed = True
            
            # Create appointment data
            appointment = AppointmentData(
                id=uuid4(),
                consumer_id=consumer.id,
                appointment_date=date.today() + timedelta(days=j),  # Spread across 2 days
                required_skills=required_skills,
                duration_min=duration_min,
                urgent=(j == 0),  # First appointment is urgent
                active=True,
                status="PENDING_TO_ASSIGN",
                location=consumer.location,
                priority="normal" if j == 0 else "low",
                task_points=5 if required_role == "RN" else 3,
                required_role=required_role,
                timing=AppointmentTiming(
                    is_timed_visit=is_timed,
                    preferred_time=time(9, 0) if is_timed else None,
                    time_flexibility_minutes=15
                ),
                relationships=AppointmentRelationships(
                    care_episode_id=f"episode_{i+1}",
                    same_provider_required=(j == 0)  # First appointment requires same provider
                ),
                pinning=AppointmentPinning(
                    is_pinned=False
                )
            )
            
            appointments.append(appointment)
    
    return appointments


def create_demo_data() -> Dict[str, Any]:
    """Create complete demo dataset for testing."""
    providers = create_demo_providers()
    consumers = create_demo_consumers()
    appointments = create_demo_appointments(consumers)
    
    return {
        "providers": providers,
        "consumers": consumers,
        "appointments": appointments
    }


def create_demo_scheduled_appointments(target_date: date) -> List['ScheduledAppointment']:
    """Create demo scheduled appointments for day planning."""
    
    # Import the planning version
    from .planning_models import ScheduledAppointment
    
    # Create demo data
    demo_data = create_demo_data()
    providers = demo_data["providers"]
    consumers = demo_data["consumers"]
    appointments = demo_data["appointments"]
    
    # Create scheduled appointments (already assigned providers and dates)
    scheduled_appointments = []
    
    for i, appointment in enumerate(appointments[:6]):  # Use first 6 appointments
        # Assign to a provider based on skills
        provider = None
        for p in providers:
            if any(skill in p.skills for skill in appointment.required_skills):
                provider = p
                break
        
        if provider is None:
            provider = providers[0]  # Fallback
        
        # Create scheduled appointment
        scheduled_appointment = ScheduledAppointment(
            id=f"scheduled_{appointment.id}",
            appointment_data=appointment,
            provider=provider,
            assigned_date=target_date,
            assigned_time=None  # Will be assigned by day planner
        )
        scheduled_appointments.append(scheduled_appointment)
    
    return scheduled_appointments


def create_demo_time_slots_for_date(target_date: date) -> List[time]:
    """Create demo time slots for a specific date."""
    
    time_slots = []
    
    # Create 30-minute slots from 8 AM to 6 PM
    start_hour = 8
    end_hour = 18
    
    for hour in range(start_hour, end_hour):
        for minute in [0, 30]:
            slot_time = time(hour, minute)
            time_slots.append(slot_time)
    
    return time_slots


def get_demo_service_configs() -> Dict[str, Dict[str, Any]]:
    """Get demo service configurations for different healthcare service types."""
    
    return {
        "skilled_nursing": {
            "service_type": "skilled_nursing",
            "required_skills": ["medication_management", "wound_care", "assessment"],
            "geographic_radius_miles": 25.0,
            "max_daily_appointments_per_provider": 8,
            "max_weekly_hours_per_provider": 40,
            "continuity_weight": 0.8,
            "workload_balance_weight": 0.6,
            "geographic_clustering_weight": 0.4,
            "patient_preference_weight": 0.7,
            "capacity_threshold_percentage": 0.9
        },
        "physical_therapy": {
            "service_type": "physical_therapy",
            "required_skills": ["physical_therapy", "mobility_training", "strength_training"],
            "geographic_radius_miles": 30.0,
            "max_daily_appointments_per_provider": 4,
            "max_weekly_hours_per_provider": 40,
            "continuity_weight": 0.9,
            "workload_balance_weight": 0.5,
            "geographic_clustering_weight": 0.3,
            "patient_preference_weight": 0.8,
            "capacity_threshold_percentage": 0.85
        },
        "personal_care": {
            "service_type": "personal_care",
            "required_skills": ["personal_care", "mobility_assistance", "housekeeping"],
            "geographic_radius_miles": 20.0,
            "max_daily_appointments_per_provider": 10,
            "max_weekly_hours_per_provider": 40,
            "continuity_weight": 0.6,
            "workload_balance_weight": 0.7,
            "geographic_clustering_weight": 0.5,
            "patient_preference_weight": 0.6,
            "capacity_threshold_percentage": 0.95
        }
    } 