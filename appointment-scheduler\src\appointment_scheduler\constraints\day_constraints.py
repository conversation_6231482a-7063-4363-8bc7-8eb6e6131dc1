"""
Production-grade day planning stage constraints for healthcare scheduling optimization.

This module contains active constraints for the second stage of optimization:
- Assigning time slots to appointments that already have providers and dates

This file now serves as a coordinator that imports and combines constraints
"""

from timefold.solver.score import constraint_provider, ConstraintFactory

# Import individual constraint modules
from .c010_schd_timeslot_availability_validation import time_slot_availability
from .c011_schd_appointment_overlap_prevention import no_double_booking
from .c012_schd_flexible_appointment_timing_optimization import appointment_duration_fit, preferred_hours
from .c013_schd_healthcare_task_sequencing import healthcare_task_sequencing
from .c014_schd_route_travel_time_optimization import travel_time_consideration
from .c015_schd_timed_appointment_pinning import provider_break_time
# Temporarily disabled due to Timefold 1.23.0b0 fundamental API bug with forEach/forEachUniquePair
# from .c016_schd_route_optimization import route_optimization_constraints


@constraint_provider
def define_day_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the day planning stage."""
    return [
        # Hard constraints - must be satisfied
        time_slot_availability(constraint_factory),
        no_double_booking(constraint_factory),
        appointment_duration_fit(constraint_factory),
        
        # Soft constraints - optimization preferences
        preferred_hours(constraint_factory),
        travel_time_consideration(constraint_factory),
        provider_break_time(constraint_factory),
        
        # Newly implemented constraints
        healthcare_task_sequencing(constraint_factory),
        
        # Route optimization constraints (provider capacity, break time, travel time, clustering)
        # Temporarily disabled due to Timefold 1.23.0b0 fundamental API bug with forEach/forEachUniquePair
        # *route_optimization_constraints(constraint_factory),
    ] 