2025-06-24 21:43:11 | INFO | Starting DayPlan job for 2025-06-24...
2025-06-24 21:43:11 | INFO | Starting day planning job for 2025-06-24: dayplan_20250624_214311
2025-06-24 21:43:11 | INFO | Route optimization disabled - using basic time slot assignment only
2025-06-24 21:43:11 | INFO | Loaded 6 appointments, 20 time slots for 2025-06-24
2025-06-24 21:43:11 | INFO | Solving time assignment problem with 6 appointments
2025-06-24 21:43:11 | INFO | Route optimization constraints enabled
2025-06-24 21:43:11 | INFO | Created solver config with 10s timeout
2025-06-24 21:43:11 | INFO | Building solver...
2025-06-24 21:43:11 | ERROR | Error in day planning job: Traceback (most recent call last):
  File "SolverFactory.java", line 107, in ai.timefold.solver.core.api.solver.SolverFactory.create
Exception: Java Exception

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\timefold\solver\_timefold_java_interop.py", line 284, in wrapped_func
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\day_constraints.py", line 28, in define_day_constraints
    time_slot_availability(constraint_factory),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py", line 16, in time_slot_availability
    .for_each(TimeSlotAssignment)
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\timefold\solver\score\_constraint_factory.py", line 58, in for_each
    return UniConstraintStream(self.delegate.forEach(source_class), self.get_default_constraint_package(),
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
java.lang.java.lang.IllegalArgumentException: java.lang.IllegalArgumentException: Cannot use class (org.jpyinterpreter.user.src.appointment_scheduler.domain.TimeSlotAssignment) in a constraint stream as it is neither the same as, nor a superclass or superinterface of one of planning entities or problem facts.
Ensure that all from(), join(), ifExists() and ifNotExists() building blocks only reference classes assignable from planning entities or problem facts ([ai.timefold.jpyinterpreter.types.datetime.PythonTime, org.jpyinterpreter.user.src.appointment_scheduler.domain.ScheduledAppointment, org.jpyinterpreter.user.src.appointment_scheduler.planning_models.TimeSlotAssignment]) annotated on the planning solution (org.jpyinterpreter.user.src.appointment_scheduler.planning_models.DaySchedule).

2025-06-24 21:43:11 | ERROR | DayPlan job failed: Traceback (most recent call last):
  File "SolverFactory.java", line 107, in ai.timefold.solver.core.api.solver.SolverFactory.create
Exception: Java Exception

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\timefold\solver\_timefold_java_interop.py", line 284, in wrapped_func
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\day_constraints.py", line 28, in define_day_constraints
    time_slot_availability(constraint_factory),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py", line 16, in time_slot_availability
    .for_each(TimeSlotAssignment)
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\timefold\solver\score\_constraint_factory.py", line 58, in for_each
    return UniConstraintStream(self.delegate.forEach(source_class), self.get_default_constraint_package(),
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
java.lang.java.lang.IllegalArgumentException: java.lang.IllegalArgumentException: Cannot use class (org.jpyinterpreter.user.src.appointment_scheduler.domain.TimeSlotAssignment) in a constraint stream as it is neither the same as, nor a superclass or superinterface of one of planning entities or problem facts.
Ensure that all from(), join(), ifExists() and ifNotExists() building blocks only reference classes assignable from planning entities or problem facts ([ai.timefold.jpyinterpreter.types.datetime.PythonTime, org.jpyinterpreter.user.src.appointment_scheduler.domain.ScheduledAppointment, org.jpyinterpreter.user.src.appointment_scheduler.planning_models.TimeSlotAssignment]) annotated on the planning solution (org.jpyinterpreter.user.src.appointment_scheduler.planning_models.DaySchedule).

2025-06-24 21:43:11 | ERROR | Scheduler failed: Traceback (most recent call last):
  File "SolverFactory.java", line 107, in ai.timefold.solver.core.api.solver.SolverFactory.create
Exception: Java Exception

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\timefold\solver\_timefold_java_interop.py", line 284, in wrapped_func
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\day_constraints.py", line 28, in define_day_constraints
    time_slot_availability(constraint_factory),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work\Scheduler\appointment-scheduler\src\appointment_scheduler\constraints\c010_schd_timeslot_availability_validation.py", line 16, in time_slot_availability
    .for_each(TimeSlotAssignment)
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\timefold\solver\score\_constraint_factory.py", line 58, in for_each
    return UniConstraintStream(self.delegate.forEach(source_class), self.get_default_constraint_package(),
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
java.lang.java.lang.IllegalArgumentException: java.lang.IllegalArgumentException: Cannot use class (org.jpyinterpreter.user.src.appointment_scheduler.domain.TimeSlotAssignment) in a constraint stream as it is neither the same as, nor a superclass or superinterface of one of planning entities or problem facts.
Ensure that all from(), join(), ifExists() and ifNotExists() building blocks only reference classes assignable from planning entities or problem facts ([ai.timefold.jpyinterpreter.types.datetime.PythonTime, org.jpyinterpreter.user.src.appointment_scheduler.domain.ScheduledAppointment, org.jpyinterpreter.user.src.appointment_scheduler.planning_models.TimeSlotAssignment]) annotated on the planning solution (org.jpyinterpreter.user.src.appointment_scheduler.planning_models.DaySchedule).

