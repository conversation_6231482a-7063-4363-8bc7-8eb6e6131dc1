"""
Production-grade assignment stage constraints for healthcare scheduling optimization.

This module contains active constraints for the first stage of optimization:
- Assigning providers and dates to appointments

This file now serves as a coordinator that imports and combines constraints
"""

from timefold.solver.score import constraint_provider, ConstraintFactory

# Import individual constraint modules
from .c001_asgn_provider_skill_validation import required_skills
from .c002_asgn_date_based_availability import provider_availability
from .c003_asgn_geographic_service_area import geographic_service_area
from .c004_asgn_timed_visit_date_assignment import provider_role_match
from .c005_asgn_workload_balance_optimization import workload_balancing
from .c006_asgn_geographic_clustering_optimization import capacity_thresholds
from .c007_asgn_patient_preference_matching import patient_preference_matching
from .c008_asgn_provider_capacity_management import provider_capacity_management
from .c009_asgn_continuity_of_care_optimization import continuity_of_care


@constraint_provider
def define_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the assignment stage."""
    return [
        # Hard constraints - must be satisfied
        required_skills(constraint_factory),
        provider_availability(constraint_factory),
        provider_role_match(constraint_factory),
        
        # Soft constraints - optimization preferences
        workload_balancing(constraint_factory),
        capacity_thresholds(constraint_factory),
        geographic_service_area(constraint_factory),
        continuity_of_care(constraint_factory),
        
        # Newly implemented constraints
        patient_preference_matching(constraint_factory),
        provider_capacity_management(constraint_factory),
    ] 