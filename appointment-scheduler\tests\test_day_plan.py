"""
Tests for the DayPlan job.
"""

import pytest
from datetime import date, timedelta
from appointment_scheduler.jobs.day_plan import <PERSON><PERSON><PERSON><PERSON><PERSON>
from appointment_scheduler.domain import BatchAssignmentResult


class TestDayPlanJob:
    """Test cases for DayPlan job."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.job = DayPlanJob()
    
    def test_job_initialization(self):
        """Test that the job initializes correctly."""
        assert self.job is not None
        assert self.job.config_manager is not None
        assert self.job.scheduler_config is not None
    
    def test_run_job_with_default_parameters(self):
        """Test running the job with default parameters."""
        result = self.job.run()
        
        # Verify result is BatchAssignmentResult
        assert isinstance(result, BatchAssignmentResult)
        
        # Verify required fields
        assert hasattr(result, 'batch_id')
        assert hasattr(result, 'total_appointments')
        assert hasattr(result, 'assigned_appointments')
        assert hasattr(result, 'unassigned_appointments')
        assert hasattr(result, 'average_score')
        assert hasattr(result, 'processing_time_seconds')
        assert hasattr(result, 'results')
        
        # Verify logical consistency
        assert result.assigned_appointments + result.unassigned_appointments == result.total_appointments
        assert result.processing_time_seconds > 0
        assert len(result.results) == result.total_appointments
    
    def test_run_job_with_target_date(self):
        """Test running the job with a specific target date."""
        target_date = date.today() + timedelta(days=1)
        result = self.job.run(target_date=target_date)
        
        assert isinstance(result, BatchAssignmentResult)
        assert result.batch_id.startswith(f"dayplan_{target_date.strftime('%Y%m%d')}")
        
        # Processing time should be reasonable (less than 30 seconds)
        assert result.processing_time_seconds < 30
    
    def test_run_job_with_batch_id(self):
        """Test running the job with a custom batch ID."""
        custom_batch_id = "test_batch_123"
        result = self.job.run(batch_id=custom_batch_id)
        
        assert isinstance(result, BatchAssignmentResult)
        assert result.batch_id == custom_batch_id
    
    def test_assignment_results_structure(self):
        """Test that assignment results have the correct structure."""
        result = self.job.run()
        
        for assignment_result in result.results:
            # Verify required fields
            assert hasattr(assignment_result, 'appointment_id')
            assert hasattr(assignment_result, 'patient_id')
            assert hasattr(assignment_result, 'provider_id')
            assert hasattr(assignment_result, 'time_slot_id')
            assert hasattr(assignment_result, 'score')
            assert hasattr(assignment_result, 'constraints_satisfied')
            assert hasattr(assignment_result, 'constraints_violated')
            
            # Verify field types
            assert isinstance(assignment_result.appointment_id, str)
            assert isinstance(assignment_result.patient_id, str)
            assert isinstance(assignment_result.provider_id, str)
            assert isinstance(assignment_result.score, (int, float))
            assert isinstance(assignment_result.constraints_satisfied, list)
            assert isinstance(assignment_result.constraints_violated, list)
    
    def test_time_slot_assignment_success(self):
        """Test that time slots are successfully assigned."""
        result = self.job.run()
        
        if result.total_appointments > 0:
            # Should have some successful assignments
            assert result.assigned_appointments > 0
            
            # Check that assigned appointments have time slots
            assigned_count = 0
            for assignment_result in result.results:
                if assignment_result.time_slot_id:
                    assigned_count += 1
            
            assert assigned_count == result.assigned_appointments
    
    def test_constraint_tracking(self):
        """Test that constraints are properly tracked."""
        result = self.job.run()
        
        for assignment_result in result.results:
            # Should have some constraint information
            total_constraints = len(assignment_result.constraints_satisfied) + len(assignment_result.constraints_violated)
            
            if assignment_result.time_slot_id:
                # Assigned appointments should have at least some satisfied constraints
                assert len(assignment_result.constraints_satisfied) > 0
            else:
                # Unassigned appointments should have violated constraints
                assert len(assignment_result.constraints_violated) > 0
    
    def test_empty_appointments_handling(self):
        """Test handling when no appointments are scheduled for the date."""
        # Test with a date far in the future (likely no appointments)
        future_date = date.today() + timedelta(days=365)
        result = self.job.run(target_date=future_date)
        
        assert isinstance(result, BatchAssignmentResult)
        
        if result.total_appointments == 0:
            assert result.assigned_appointments == 0
            assert result.unassigned_appointments == 0
            assert len(result.results) == 0
            assert result.average_score == 0.0
    
    def test_processing_time_tracking(self):
        """Test that processing time is properly tracked."""
        result = self.job.run()
        
        # Processing time should be positive and reasonable
        assert result.processing_time_seconds > 0
        assert result.processing_time_seconds < 60  # Should complete within 1 minute
    
    def test_score_calculation(self):
        """Test that scores are calculated correctly."""
        result = self.job.run()
        
        if result.total_appointments > 0:
            # Average score should be calculated
            if result.results:
                calculated_average = sum(r.score for r in result.results) / len(result.results)
                assert abs(result.average_score - calculated_average) < 0.01
    
    def test_provider_assignment_distribution(self):
        """Test that appointments are distributed across providers."""
        result = self.job.run()
        
        if result.assigned_appointments > 1:
            # Check provider distribution
            provider_counts = {}
            for assignment_result in result.results:
                if assignment_result.time_slot_id:  # Only count assigned appointments
                    provider_id = assignment_result.provider_id
                    provider_counts[provider_id] = provider_counts.get(provider_id, 0) + 1
            
            # Should have at least one provider with assignments
            assert len(provider_counts) > 0
            
            # No single provider should have all assignments (unless only one provider)
            if len(provider_counts) > 1:
                max_assignments = max(provider_counts.values())
                assert max_assignments < result.assigned_appointments
