# Main Scheduler Configuration
# This file contains global settings for the appointment scheduling system

# Rolling window configuration
rolling_window_days: 7
batch_size: 100
max_solving_time_seconds: 10  # Reduced from 300 for testing

# Configuration paths
config_folder: "config"
log_level: "INFO"

# Feature toggles
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true

# Scheduling settings
default_schedule_time: "02:00"  # Time to run nightly jobs (HH:MM)
rolling_interval_hours: 24      # Hours between rolling window jobs

# Database settings (for future integration)
database_connection_timeout: 30
database_pool_size: 10

# Performance settings
max_concurrent_jobs: 1
job_timeout_seconds: 600

# Notification settings
enable_email_notifications: false
enable_slack_notifications: false
notification_recipients: []

# Monitoring settings
enable_metrics_collection: true
metrics_retention_days: 30

# Logging settings
log_rotation: "1 day"
log_retention: "30 days"
log_format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"

# Geographic settings
default_geographic_radius_miles: 25.0
enable_geocoding: true

# Provider settings
default_max_daily_appointments: 8
default_max_weekly_hours: 40
enable_overtime_penalties: true

# Patient settings
enable_patient_preferences: true
enable_continuity_bonus: true
continuity_threshold_days: 30

# New settings from the code block
rolling_window_start_hour: 8
rolling_window_end_hour: 18
parallel_solving: true
max_threads: 4
memory_limit_mb: 1024
log_rotation_days: 7
enable_metrics: true
metrics_port: 8080
database_url: "sqlite:///appointments.db"
connection_pool_size: 10 