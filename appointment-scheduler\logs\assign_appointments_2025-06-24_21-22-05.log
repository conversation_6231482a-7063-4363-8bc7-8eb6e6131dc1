2025-06-24 21:22:05,179 - root - INFO - Logger initialized. Log file: D:\Work\Scheduler\appointment-scheduler\logs\assign_appointments_2025-06-24_21-22-05.log
2025-06-24 21:22:05,180 - __main__ - INFO - Initializing AssignAppointmentJob...
2025-06-24 21:22:05,185 - src.appointment_scheduler.config_manager - INFO - Loaded scheduler configuration from config\scheduler.yml
2025-06-24 21:22:05,189 - src.appointment_scheduler.config_manager - INFO - Loaded service config for skilled_nursing: config\skilled_nursing.yml
2025-06-24 21:22:05,193 - src.appointment_scheduler.config_manager - INFO - Loaded service config for physical_therapy: config\physical_therapy.yml
2025-06-24 21:22:13,638 - __main__ - INFO - AssignAppointment job initialized with solver config
2025-06-24 21:22:13,639 - __main__ - INFO - Running job for target date: 2025-06-24
2025-06-24 21:22:13,639 - __main__ - INFO - 🚀 === ASSIGNMENT JOB STARTED ===
2025-06-24 21:22:13,639 - __main__ - INFO - 📅 Target Date: 2025-06-24
2025-06-24 21:22:13,640 - __main__ - INFO - 🏥 Service Type: All Services
2025-06-24 21:22:13,640 - __main__ - INFO - === STAGE 1: Loading Data ===
2025-06-24 21:22:13,641 - src.appointment_scheduler.data_loader - INFO - 🚀 Starting data loading process...
2025-06-24 21:22:13,641 - src.appointment_scheduler.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-24 21:22:13,641 - src.appointment_scheduler.data_loader - INFO - Reading provider data from: data\providers.yml
2025-06-24 21:22:13,655 - src.appointment_scheduler.data_loader - INFO - ✅ Provider data loaded: 6 providers
2025-06-24 21:22:13,655 - src.appointment_scheduler.data_loader - INFO -    - Sarah Johnson, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-24 21:22:13,656 - src.appointment_scheduler.data_loader - INFO -    - Michael Chen, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-24 21:22:13,656 - src.appointment_scheduler.data_loader - INFO -    - Lisa Rodriguez, LPN (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-24 21:22:13,656 - src.appointment_scheduler.data_loader - INFO -    - David Wilson, LPN (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-24 21:22:13,657 - src.appointment_scheduler.data_loader - INFO -    - Maria Garcia, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-24 21:22:13,657 - src.appointment_scheduler.data_loader - INFO -    - James Thompson, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-24 21:22:13,657 - src.appointment_scheduler.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-24 21:22:13,657 - src.appointment_scheduler.data_loader - INFO - Reading consumer data from: data\consumers.yml
2025-06-24 21:22:13,670 - src.appointment_scheduler.data_loader - INFO - ✅ Consumer data loaded: 8 consumers
2025-06-24 21:22:13,670 - src.appointment_scheduler.data_loader - INFO -    - Margaret Smith - Episode: episode_001
2025-06-24 21:22:13,671 - src.appointment_scheduler.data_loader - INFO -    - Robert Johnson - Episode: episode_002
2025-06-24 21:22:13,671 - src.appointment_scheduler.data_loader - INFO -    - Carmen Rodriguez - Episode: episode_003
2025-06-24 21:22:13,672 - src.appointment_scheduler.data_loader - INFO -    - Jose Martinez - Episode: episode_004
2025-06-24 21:22:13,672 - src.appointment_scheduler.data_loader - INFO -    - Jennifer Williams - Episode: episode_005
2025-06-24 21:22:13,672 - src.appointment_scheduler.data_loader - INFO -    - Thomas Brown - Episode: episode_006
2025-06-24 21:22:13,674 - src.appointment_scheduler.data_loader - INFO -    - Patricia Davis - Episode: episode_007
2025-06-24 21:22:13,674 - src.appointment_scheduler.data_loader - INFO -    - Richard Miller - Episode: episode_008
2025-06-24 21:22:13,675 - src.appointment_scheduler.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-24 21:22:13,675 - src.appointment_scheduler.data_loader - INFO - Reading appointment data from: data\appointments.yml
2025-06-24 21:22:13,700 - src.appointment_scheduler.data_loader - INFO - ✅ Appointment data loaded: 8 appointments
2025-06-24 21:22:13,700 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT RN - 60min - 2025-06-24
2025-06-24 21:22:13,701 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular RN - 60min - 2025-06-25
2025-06-24 21:22:13,701 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT PT - 45min - 2025-06-24
2025-06-24 21:22:13,701 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular PT - 45min - 2025-06-25
2025-06-24 21:22:13,701 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT CNA - 30min - 2025-06-24
2025-06-24 21:22:13,702 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular CNA - 30min - 2025-06-25
2025-06-24 21:22:13,702 - src.appointment_scheduler.data_loader - INFO -    - 🚨 URGENT LPN - 30min - 2025-06-24
2025-06-24 21:22:13,703 - src.appointment_scheduler.data_loader - INFO -    - 📅 Regular LPN - 30min - 2025-06-25
2025-06-24 21:22:13,704 - src.appointment_scheduler.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-24 21:22:13,705 - src.appointment_scheduler.data_loader - INFO - 📊 Total records loaded:
2025-06-24 21:22:13,706 - src.appointment_scheduler.data_loader - INFO -    - Providers: 6
2025-06-24 21:22:13,706 - src.appointment_scheduler.data_loader - INFO -    - Consumers: 8
2025-06-24 21:22:13,706 - src.appointment_scheduler.data_loader - INFO -    - Appointments: 8
2025-06-24 21:22:13,707 - src.appointment_scheduler.data_loader - INFO - ✅ All data loaded successfully!
2025-06-24 21:22:13,708 - __main__ - INFO - 📊 Initial data loaded: 6 providers, 8 consumers, 8 appointments
2025-06-24 21:22:13,708 - __main__ - INFO - === STAGE 2A: No service type filter applied ===
2025-06-24 21:22:13,709 - __main__ - INFO - ✅ Using all 8 appointments
2025-06-24 21:22:13,709 - __main__ - INFO - === STAGE 2B: Filtering by Date Range ===
2025-06-24 21:22:13,710 - __main__ - INFO - 📅 Date range: 2025-06-24 to 2025-06-30
2025-06-24 21:22:13,710 - __main__ - INFO - ✅ Filtered to 8 appointments for date range
2025-06-24 21:22:13,710 - __main__ - INFO - === STAGE 3: Creating Planning Entities ===
2025-06-24 21:22:13,711 - __main__ - INFO - ✅ Created 8 planning entities
2025-06-24 21:22:13,711 - __main__ - INFO - === STAGE 4: Creating Available Dates ===
2025-06-24 21:22:13,712 - __main__ - INFO - ✅ Created 5 available dates: ['2025-06-24', '2025-06-25', '2025-06-26', '2025-06-27', '2025-06-30']
2025-06-24 21:22:13,712 - __main__ - INFO - === STAGE 5: Creating Optimization Solution ===
2025-06-24 21:22:13,712 - __main__ - INFO - ✅ Solution created with 8 assignments, 5 available dates
2025-06-24 21:22:13,713 - __main__ - INFO - === STAGE 6: Starting Optimization Solver ===
2025-06-24 21:22:13,713 - __main__ - INFO - 🔧 Starting solver with 8 assignments
2025-06-24 21:22:13,713 - __main__ - INFO - 🏥 No specific service type - using default constraints
2025-06-24 21:22:13,714 - __main__ - INFO - 🏥 Using default service config: skilled_nursing
2025-06-24 21:22:13,714 - __main__ - INFO - 🔧 Creating solver instance...
2025-06-24 21:22:13,827 - __main__ - INFO - ⏱️  Solver time limit: 10 seconds
2025-06-24 21:22:13,828 - __main__ - INFO - 🚀 Starting optimization process...
2025-06-24 21:22:13,828 - __main__ - INFO -    - Applying constraint rules...
2025-06-24 21:22:13,828 - __main__ - INFO -    - Balancing workload across providers...
2025-06-24 21:22:13,829 - __main__ - INFO -    - Optimizing geographic distribution...
2025-06-24 21:22:13,829 - __main__ - INFO -    - Considering patient preferences...
2025-06-24 21:22:14,074 - timefold.solver - INFO - Solving started: time spent (94), best score (0hard/0soft), environment mode (PHASE_ASSERT), move thread count (NONE), random (JDK with seed 0).
2025-06-24 21:22:14,077 - timefold.solver - INFO - Problem scale: entity count (8), variable count (16), approximate value count (11), approximate problem scale (6.560962 × 10^11).
2025-06-24 21:22:21,437 - timefold.solver - INFO - Construction Heuristic phase (0) ended: time spent (7474), best score (-6hard/-22soft), move evaluation speed (32/sec), step total (8).
2025-06-24 21:22:51,777 - timefold.solver - INFO - Local Search phase (1) ended: time spent (37814), best score (-6hard/-22soft), move evaluation speed (9/sec), step total (101).
2025-06-24 21:22:51,779 - timefold.solver - INFO - Solving ended: time spent (37815), best score (-6hard/-22soft), move evaluation speed (13/sec), phase total (2), environment mode (PHASE_ASSERT), move thread count (NONE).
2025-06-24 21:22:51,846 - __main__ - INFO - ✅ Solver completed successfully!
2025-06-24 21:22:51,846 - __main__ - INFO - 📊 Final score: -6hard/-22soft
2025-06-24 21:22:51,846 - __main__ - INFO - ✅ Optimization solver completed
2025-06-24 21:22:51,847 - __main__ - INFO - === STAGE 7: Processing Results ===
2025-06-24 21:22:51,848 - __main__ - INFO - 📊 Processing assignment results...
2025-06-24 21:22:51,848 - __main__ - INFO - 🔍 Analyzing assignment results...
2025-06-24 21:22:51,849 - __main__ - INFO - ✅ ASSIGNED: Margaret Smith -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:22:51,849 - __main__ - INFO - ✅ ASSIGNED: Margaret Smith -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:22:51,849 - __main__ - INFO - ✅ ASSIGNED: Robert Johnson -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:22:51,850 - __main__ - INFO - ✅ ASSIGNED: Robert Johnson -> Sarah Johnson, RN on 2025-06-24
2025-06-24 21:22:51,851 - __main__ - INFO - ✅ ASSIGNED: Carmen Rodriguez -> Maria Garcia, CNA on 2025-06-24
2025-06-24 21:22:51,851 - __main__ - INFO - ✅ ASSIGNED: Carmen Rodriguez -> Maria Garcia, CNA on 2025-06-24
2025-06-24 21:22:51,852 - __main__ - INFO - ✅ ASSIGNED: Jose Martinez -> Lisa Rodriguez, LPN on 2025-06-24
2025-06-24 21:22:51,852 - __main__ - INFO - ✅ ASSIGNED: Jose Martinez -> Lisa Rodriguez, LPN on 2025-06-24
2025-06-24 21:22:51,852 - __main__ - INFO - 📈 Calculating assignment statistics...
2025-06-24 21:22:51,853 - __main__ - INFO - === ASSIGNMENT RESULTS SUMMARY ===
2025-06-24 21:22:51,853 - __main__ - INFO - 📊 Total appointments: 8
2025-06-24 21:22:51,854 - __main__ - INFO - ✅ Assigned: 8 (100.0%)
2025-06-24 21:22:51,854 - __main__ - INFO - ❌ Unassigned: 0
2025-06-24 21:22:51,855 - __main__ - INFO - ⏱️  Processing time: 38.21 seconds
2025-06-24 21:22:51,855 - __main__ - INFO - 📈 Final score: -6hard/-22soft
2025-06-24 21:22:51,856 - __main__ - INFO - === SERVICE TYPE STATISTICS ===
2025-06-24 21:22:51,856 - __main__ - INFO - 🏥 skilled_nursing: 2/2 (100.0%)
2025-06-24 21:22:51,857 - __main__ - INFO - 🏥 physical_therapy: 2/2 (100.0%)
2025-06-24 21:22:51,857 - __main__ - INFO - 🏥 personal_care: 2/2 (100.0%)
2025-06-24 21:22:51,857 - __main__ - INFO - 🏥 general: 2/2 (100.0%)
2025-06-24 21:22:51,858 - __main__ - INFO - === CONSTRAINT SUMMARY ===
2025-06-24 21:22:51,858 - __main__ - INFO - ✅ Satisfied constraints: 28
2025-06-24 21:22:51,858 - __main__ - INFO - ❌ Violated constraints: 4
2025-06-24 21:22:51,859 - __main__ - INFO -    ❌ required_provider_skills: 2
2025-06-24 21:22:51,859 - __main__ - INFO -    ❌ provider_role_match: 2
2025-06-24 21:22:51,859 - __main__ - INFO - 🎉 === ASSIGNMENT JOB COMPLETED ===
2025-06-24 21:22:51,860 - __main__ - INFO - ⏱️  Total processing time: 38.21 seconds
