"""
Time Slot Availability Validation Constraint (C010)

This constraint ensures that time slots are available for assignment.
This is a HARD constraint that must be satisfied for valid assignments.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from ..planning_models import TimeSlotAssignment
def time_slot_availability(constraint_factory: ConstraintFactory) -> Constraint:
    """Time slot must be available for assignment."""
    # For now, all time slots are considered available since we're using simple time objects
    # In a real implementation, this would check against a database or availability service
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: False)  # No constraints for now - all time slots are available
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint("Time slot availability"))