"""
Production-grade base constraint utilities and helper functions.

This module contains robust utility functions used across different constraint types.
"""

from datetime import datetime, time, timedelta, date
from math import radians, cos, sin, asin, sqrt
from typing import List, Dict, Any, Optional

from ..domain import Provider, Location, AppointmentData, weekday_from_int
def _provider_serves_location(provider: Provider, location: Location) -> bool:
    """Check if provider serves the given location."""
    # Simplified implementation - always return True
    # In a real implementation, this would check geographic distance
    return True


def _provider_available_on_date(provider: Provider, target_date) -> bool:
    """Check if provider is available on the given date."""
    # Simplified implementation - always return True
    # In a real implementation, this would check provider's schedule
    return True


def _calculate_distance(loc1: Location, loc2: Location) -> float:
    """Calculate distance between two locations in miles using Haversine formula."""
    if loc1 is None or loc2 is None:
        return 0.0
    
    # Convert decimal degrees to radians
    lat1, lon1 = radians(loc1.latitude), radians(loc1.longitude)
    lat2, lon2 = radians(loc2.latitude), radians(loc2.longitude)
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    
    # Radius of Earth in miles
    r = 3956
    
    return c * r


def _is_within_service_radius(provider: Provider, appointment_location: Location, max_radius_miles: float = 25.0) -> bool:
    """Check if appointment location is within provider's service radius."""
    if provider.home_location is None or appointment_location is None:
        return True  # Default to True if location data is missing
    
    distance = _calculate_distance(provider.home_location, appointment_location)
    return distance <= max_radius_miles


def _has_required_skills(provider: Provider, required_skills: List[str]) -> bool:
    """Check if provider has all required skills."""
    if required_skills is None or len(required_skills) == 0:
        return True
    return all(skill in provider.skills for skill in required_skills)


def _is_working_day(provider: Provider, target_date: date) -> bool:
    """Check if the target date is a working day for the provider."""
    weekday = target_date.weekday()
    weekday_enum = weekday_from_int(weekday)
    
    # Check if provider has availability configuration
    if provider.availability is not None:
        return weekday_enum in provider.availability.working_days
    
    # Default: Monday-Friday (0=Monday, 6=Sunday)
    return weekday < 5


def _get_provider_workload_for_date(provider: Provider, target_date: date, assignments) -> int:
    """Get the number of appointments assigned to a provider on a specific date."""
    count = 0
    for assignment in assignments:
        if (assignment.provider == provider and 
            assignment.assigned_date == target_date):
            count += 1
    return count


def _calculate_travel_time_between_appointments(appointment1: AppointmentData, appointment2: AppointmentData) -> int:
    """Calculate travel time in minutes between two appointments."""
    if appointment1.location is None or appointment2.location is None:
        return 0
    
    distance = _calculate_distance(appointment1.location, appointment2.location)
    
    # Adjust speed based on urban vs rural areas
    if _is_urban_area(appointment1.location) and _is_urban_area(appointment2.location):
        avg_speed_mph = 25  # Urban traffic
    else:
        avg_speed_mph = 45  # Rural/suburban roads
    
    travel_time_hours = distance / avg_speed_mph
    return int(travel_time_hours * 60)


def _is_sufficient_travel_time(appointment1: AppointmentData, appointment2: AppointmentData, 
                              min_travel_time_minutes: int = 30) -> bool:
    """Check if there's sufficient travel time between appointments."""
    travel_time = _calculate_travel_time_between_appointments(appointment1, appointment2)
    return travel_time >= min_travel_time_minutes


def _get_appointment_duration_minutes(appointment: AppointmentData) -> int:
    """Get the duration of an appointment in minutes."""
    return appointment.duration_min


def _is_urgent_appointment(appointment: AppointmentData) -> bool:
    """Check if an appointment is marked as urgent."""
    return appointment.urgent


def _get_service_type_from_skills(required_skills: List[str]) -> str:
    """Determine service type based on required skills."""
    skill_mapping = {
        "medication_management": "skilled_nursing",
        "wound_care": "skilled_nursing", 
        "assessment": "skilled_nursing",
        "iv_therapy": "skilled_nursing",
        "diabetes_management": "skilled_nursing",
        "physical_therapy": "physical_therapy",
        "mobility_training": "physical_therapy",
        "strength_training": "physical_therapy",
        "rehabilitation": "physical_therapy",
        "personal_care": "personal_care",
        "mobility_assistance": "personal_care",
        "housekeeping": "personal_care",
        "meal_assistance": "personal_care",
        "companionship": "personal_care",
        "medication_administration": "general",
        "vital_signs": "general",
        "basic_care": "general",
        "catheter_care": "general",
        "ostomy_care": "general"
    }
    
    for skill in required_skills:
        if skill in skill_mapping:
            return skill_mapping[skill]
    
    return "general"


def _is_urban_area(location: Location) -> bool:
    """Determine if a location is in an urban area based on population density."""
    # Simplified implementation - in production, this would use GIS data
    # For now, assume locations in major cities are urban
    urban_cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", 
                   "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"]
    
    return location.city in urban_cities if location.city is not None else False


def _calculate_provider_utilization(provider: Provider, assignments, target_date: date) -> float:
    """Calculate provider utilization percentage for a given date."""
    total_assigned_minutes = 0
    max_available_minutes = provider.capacity.max_hours_per_day * 60
    
    for assignment in assignments:
        if (assignment.provider == provider and 
            assignment.assigned_date == target_date):
            total_assigned_minutes += assignment.appointment_data.duration_min
    
    return (total_assigned_minutes / max_available_minutes * 100) if max_available_minutes > 0 else 0


def _is_provider_overbooked(provider: Provider, assignments, target_date: date) -> bool:
    """Check if provider is overbooked for a given date."""
    utilization = _calculate_provider_utilization(provider, assignments, target_date)
    return utilization > 100.0


def _get_consecutive_appointments_count(provider: Provider, assignments, target_date: date) -> int:
    """Get the maximum number of consecutive appointments for a provider on a given date."""
    date_assignments = [a for a in assignments if a.provider == provider and a.assigned_date == target_date]
    
    if not date_assignments:
        return 0
    
    # Sort by time (if available) or by appointment ID for consistency
    date_assignments.sort(key=lambda x: x.appointment_data.id)
    
    max_consecutive = 1
    current_consecutive = 1
    
    for i in range(1, len(date_assignments)):
        # Check if appointments are consecutive (simplified logic)
        current_consecutive += 1
        max_consecutive = max(max_consecutive, current_consecutive)
    
    return max_consecutive


def _validate_appointment_constraints(appointment: AppointmentData, provider: Provider, 
                                    target_date: date) -> Dict[str, bool]:
    """Validate all constraints for a specific appointment assignment."""
    return {
        "has_required_skills": _has_required_skills(provider, appointment.required_skills),
        "is_working_day": _is_working_day(provider, target_date),
        "within_service_radius": _is_within_service_radius(provider, appointment.location) if appointment.location else True,
        "role_matches": provider.role == appointment.required_role if appointment.required_role else True,
        "duration_appropriate": appointment.duration_min <= provider.capacity.max_hours_per_day * 60
    } 